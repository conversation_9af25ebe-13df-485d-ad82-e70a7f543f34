"""Comprehensive tests for the HTTPx authentication hook system."""

import asyncio
import time
from pathlib import Path
from tempfile import NamedTemporaryFile
from unittest.mock import AsyncMock, Mock, patch

import httpx
import pytest

from skaha.hooks.httpx.auth import (
    AuthenticationError,
    create_auth_hook,
    _handle_auth_async,
    _handle_auth_sync,
    _refresh_oidc_async,
    _refresh_oidc_sync,
    _refresh_x509_async,
    _refresh_x509_sync,
    _update_request_headers,
)
from skaha.models.auth import Authentication, OIDC, X509


class TestCreateAuthHook:
    """Test the create_auth_hook function."""

    def test_create_auth_hook_returns_tuple(self):
        """Test that create_auth_hook returns a tuple of callables."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        sync_hook, async_hook = create_auth_hook(client)

        assert callable(sync_hook)
        assert callable(async_hook)

    def test_sync_hook_signature(self):
        """Test that sync hook has correct signature."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        sync_hook, _ = create_auth_hook(client)

        # Create a mock request
        mock_request = Mock(spec=httpx.Request)

        # Should not raise any exception for signature
        with patch("skaha.hooks.httpx.auth._handle_auth_sync") as mock_handle:
            sync_hook(mock_request)
            mock_handle.assert_called_once_with(client, mock_request)

    @pytest.mark.asyncio
    async def test_async_hook_signature(self):
        """Test that async hook has correct signature."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        _, async_hook = create_auth_hook(client)

        # Create a mock request
        mock_request = Mock(spec=httpx.Request)

        # Should not raise any exception for signature
        with patch("skaha.hooks.httpx.auth._handle_auth_async") as mock_handle:
            await async_hook(mock_request)
            mock_handle.assert_called_once_with(client, mock_request)


class TestHandleAuthSync:
    """Test the _handle_auth_sync function."""

    def test_skip_refresh_for_user_token(self):
        """Test that refresh is skipped for user-provided tokens."""
        from pydantic import SecretStr
        from skaha.client import SkahaClient

        client = SkahaClient(token=SecretStr("user-token"))
        mock_request = Mock(spec=httpx.Request)

        with patch("skaha.hooks.httpx.auth.log") as mock_log:
            _handle_auth_sync(client, mock_request)
            mock_log.debug.assert_called_with(
                "User-provided credentials detected, skipping auth refresh"
            )

    def test_skip_refresh_for_user_certificate(self):
        """Test that refresh is skipped for user-provided certificates."""
        from skaha.client import SkahaClient

        with NamedTemporaryFile(suffix=".pem") as temp_file:
            client = SkahaClient(certificate=Path(temp_file.name))
            mock_request = Mock(spec=httpx.Request)

            with patch("skaha.hooks.httpx.auth.log") as mock_log:
                _handle_auth_sync(client, mock_request)
                mock_log.debug.assert_called_with(
                    "User-provided credentials detected, skipping auth refresh"
                )

    def test_skip_refresh_for_valid_auth(self):
        """Test that refresh is skipped when auth is not expired."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=False):
            with patch("skaha.hooks.httpx.auth.log") as mock_log:
                _handle_auth_sync(client, mock_request)
                mock_log.debug.assert_called_with(
                    "Authentication not expired, continuing with request"
                )

    def test_refresh_x509_mode(self):
        """Test refresh for x509 authentication mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "x509"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth._refresh_x509_sync") as mock_refresh:
                with patch("skaha.hooks.httpx.auth._update_request_headers") as mock_update:
                    with patch.object(client, "save") as mock_save:
                        _handle_auth_sync(client, mock_request)

                        mock_refresh.assert_called_once_with(client)
                        mock_update.assert_called_once_with(client, mock_request)
                        mock_save.assert_called_once()

    def test_refresh_default_mode(self):
        """Test refresh for default authentication mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "default"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth._refresh_x509_sync") as mock_refresh:
                with patch("skaha.hooks.httpx.auth._update_request_headers") as mock_update:
                    with patch.object(client, "save") as mock_save:
                        _handle_auth_sync(client, mock_request)

                        mock_refresh.assert_called_once_with(client)
                        mock_update.assert_called_once_with(client, mock_request)
                        mock_save.assert_called_once()

    def test_refresh_oidc_mode(self):
        """Test refresh for OIDC authentication mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth._refresh_oidc_sync") as mock_refresh:
                with patch("skaha.hooks.httpx.auth._update_request_headers") as mock_update:
                    with patch.object(client, "save") as mock_save:
                        _handle_auth_sync(client, mock_request)

                        mock_refresh.assert_called_once_with(client)
                        mock_update.assert_called_once_with(client, mock_request)
                        mock_save.assert_called_once()

    def test_unknown_auth_mode_raises_error(self):
        """Test that unknown auth mode raises AuthenticationError."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "unknown"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with pytest.raises(AuthenticationError, match="Unknown authentication mode"):
                _handle_auth_sync(client, mock_request)

    def test_refresh_failure_raises_error(self):
        """Test that refresh failure raises AuthenticationError."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "x509"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth._refresh_x509_sync", side_effect=Exception("Refresh failed")):
                with pytest.raises(AuthenticationError, match="Failed to refresh authentication"):
                    _handle_auth_sync(client, mock_request)


class TestHandleAuthAsync:
    """Test the _handle_auth_async function."""

    @pytest.mark.asyncio
    async def test_skip_refresh_for_user_token(self):
        """Test that refresh is skipped for user-provided tokens."""
        from pydantic import SecretStr
        from skaha.client import SkahaClient

        client = SkahaClient(token=SecretStr("user-token"))
        mock_request = Mock(spec=httpx.Request)

        with patch("skaha.hooks.httpx.auth.log") as mock_log:
            await _handle_auth_async(client, mock_request)
            mock_log.debug.assert_called_with(
                "User-provided credentials detected, skipping auth refresh"
            )

    @pytest.mark.asyncio
    async def test_refresh_oidc_mode(self):
        """Test refresh for OIDC authentication mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth._refresh_oidc_async") as mock_refresh:
                with patch("skaha.hooks.httpx.auth._update_request_headers") as mock_update:
                    with patch.object(client, "save") as mock_save:
                        await _handle_auth_async(client, mock_request)

                        mock_refresh.assert_called_once_with(client)
                        mock_update.assert_called_once_with(client, mock_request)
                        mock_save.assert_called_once()

    @pytest.mark.asyncio
    async def test_unknown_auth_mode_raises_error(self):
        """Test that unknown auth mode raises AuthenticationError."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "unknown"
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with pytest.raises(AuthenticationError, match="Unknown authentication mode"):
                await _handle_auth_async(client, mock_request)


class TestRefreshX509:
    """Test X.509 certificate refresh functions."""

    def test_refresh_x509_sync_default_mode(self):
        """Test X.509 refresh for default mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "default"

        with patch("skaha.hooks.httpx.auth.x509.authenticate") as mock_auth:
            mock_auth.return_value = client.auth.default
            _refresh_x509_sync(client)
            mock_auth.assert_called_once_with(client.auth.default)

    def test_refresh_x509_sync_x509_mode(self):
        """Test X.509 refresh for x509 mode."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "x509"

        with patch("skaha.hooks.httpx.auth.x509.authenticate") as mock_auth:
            mock_auth.return_value = client.auth.x509
            _refresh_x509_sync(client)
            mock_auth.assert_called_once_with(client.auth.x509)

    @pytest.mark.asyncio
    async def test_refresh_x509_async(self):
        """Test X.509 async refresh."""
        from skaha.client import SkahaClient

        client = SkahaClient()

        with patch("skaha.hooks.httpx.auth._refresh_x509_sync") as mock_sync:
            await _refresh_x509_async(client)
            # Should call the sync version in executor
            mock_sync.assert_called_once_with(client)


class TestRefreshOIDC:
    """Test OIDC token refresh functions."""

    def test_refresh_oidc_sync_invalid_config(self):
        """Test OIDC sync refresh with invalid configuration."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"

        with patch.object(client.auth.oidc, "valid", return_value=False):
            with pytest.raises(AuthenticationError, match="OIDC configuration is invalid"):
                _refresh_oidc_sync(client)

    @pytest.mark.asyncio
    async def test_refresh_oidc_async_invalid_config(self):
        """Test OIDC async refresh with invalid configuration."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"

        with patch.object(client.auth.oidc, "valid", return_value=False):
            with pytest.raises(AuthenticationError, match="OIDC configuration is invalid"):
                await _refresh_oidc_async(client)

    @pytest.mark.asyncio
    async def test_refresh_oidc_async_success(self):
        """Test successful OIDC async refresh."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"
        client.auth.oidc.endpoints.token = "https://example.com/token"
        client.auth.oidc.client.identity = "client-id"
        client.auth.oidc.client.secret = "client-secret"
        client.auth.oidc.token.refresh = "refresh-token"

        mock_tokens = {
            "access_token": "new-access-token",
            "expires_in": 3600,
            "refresh_token": "new-refresh-token",
        }

        with patch.object(client.auth.oidc, "valid", return_value=True):
            with patch("skaha.hooks.httpx.auth.oidc.refresh_token", return_value=mock_tokens) as mock_refresh:
                await _refresh_oidc_async(client)

                mock_refresh.assert_called_once_with(
                    token_url="https://example.com/token",
                    client_id="client-id",
                    client_secret="client-secret",
                    refresh_token="refresh-token",
                )

                assert client.auth.oidc.token.access == "new-access-token"
                assert client.auth.oidc.token.refresh == "new-refresh-token"

    @pytest.mark.asyncio
    async def test_refresh_oidc_async_invalid_refresh_token(self):
        """Test OIDC async refresh with invalid refresh token."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"

        with patch.object(client.auth.oidc, "valid", return_value=True):
            with patch("skaha.hooks.httpx.auth.oidc.refresh_token", side_effect=ValueError("Invalid token")):
                with pytest.raises(AuthenticationError, match="Refresh token invalid"):
                    await _refresh_oidc_async(client)


class TestUpdateRequestHeaders:
    """Test the _update_request_headers function."""

    def test_update_request_headers(self):
        """Test that request headers are updated correctly."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        mock_request = Mock(spec=httpx.Request)
        mock_request.headers = {}

        fresh_headers = {
            "Authorization": "Bearer new-token",
            "X-Skaha-Authentication-Type": "oidc",
        }

        with patch.object(client, "_get_headers", return_value=fresh_headers):
            _update_request_headers(client, mock_request)

            assert mock_request.headers["Authorization"] == "Bearer new-token"
            assert mock_request.headers["X-Skaha-Authentication-Type"] == "oidc"


class TestOIDCRefreshTokenFunction:
    """Test the OIDC refresh_token function directly."""

    @pytest.mark.asyncio
    async def test_refresh_token_success(self):
        """Test successful token refresh."""
        from skaha.auth.oidc import refresh

        mock_response_data = {
            "access_token": "new-access-token",
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": "new-refresh-token",
        }

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_response_data

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response

            result = await refresh(
                url="https://example.com/token",
                identity="test-client",
                secret="test-secret",
                token="test-refresh-token",
            )

            assert result == mock_response_data
            mock_client.post.assert_called_once_with(
                "https://example.com/token",
                data={
                    "grant_type": "refresh_token",
                    "refresh_token": "test-refresh-token",
                    "client_id": "test-client",
                    "client_secret": "test-secret",
                },
                auth=("test-client", "test-secret"),
            )

    @pytest.mark.asyncio
    async def test_refresh_token_invalid_grant(self):
        """Test refresh token with invalid grant error."""
        from skaha.auth.oidc import refresh

        mock_response_data = {
            "error": "invalid_grant",
            "error_description": "The refresh token is invalid or expired",
        }

        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = mock_response_data

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response

            with pytest.raises(ValueError, match="Refresh token is invalid or expired"):
                await refresh(
                    url="https://example.com/token",
                    identity="test-client",
                    secret="test-secret",
                    token="invalid-refresh-token",
                )

    @pytest.mark.asyncio
    async def test_refresh_token_with_existing_client(self):
        """Test refresh token using existing httpx client."""
        from skaha.auth.oidc import refresh

        mock_response_data = {
            "access_token": "new-access-token",
            "expires_in": 3600,
        }

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_response_data

        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response

        result = await refresh(
            url="https://example.com/token",
            identity="test-client",
            secret="test-secret",
            token="test-refresh-token",
            client=mock_client,
        )

        assert result == mock_response_data
        mock_client.post.assert_called_once()


class TestIntegration:
    """Integration tests for the authentication hook system."""

    def test_skaha_client_has_auth_hooks(self):
        """Test that SkahaClient includes authentication hooks."""
        from skaha.client import SkahaClient

        client = SkahaClient()

        # Test sync client kwargs
        sync_kwargs = client._get_client_kwargs(is_async=False)
        assert "event_hooks" in sync_kwargs
        assert "request" in sync_kwargs["event_hooks"]
        assert len(sync_kwargs["event_hooks"]["request"]) == 1

        # Test async client kwargs
        async_kwargs = client._get_client_kwargs(is_async=True)
        assert "event_hooks" in async_kwargs
        assert "request" in async_kwargs["event_hooks"]
        assert len(async_kwargs["event_hooks"]["request"]) == 1

    def test_auth_hook_integration_with_expired_auth(self):
        """Test auth hook integration when authentication is expired."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "x509"

        # Create a mock request
        mock_request = Mock(spec=httpx.Request)
        mock_request.headers = {}

        # Get the auth hook
        sync_hook, _ = create_auth_hook(client)

        with patch.object(client.auth, "expired", return_value=True):
            with patch("skaha.hooks.httpx.auth.x509.authenticate") as mock_auth:
                with patch.object(client, "_get_headers", return_value={"Authorization": "Bearer new-token"}):
                    with patch.object(client, "save") as mock_save:
                        # This should trigger the auth refresh
                        sync_hook(mock_request)

                        mock_auth.assert_called_once()
                        mock_save.assert_called_once()
                        assert mock_request.headers["Authorization"] == "Bearer new-token"

    @pytest.mark.asyncio
    async def test_async_auth_hook_integration(self):
        """Test async auth hook integration."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "oidc"

        # Create a mock request
        mock_request = Mock(spec=httpx.Request)
        mock_request.headers = {}

        # Get the auth hook
        _, async_hook = create_auth_hook(client)

        with patch.object(client.auth, "expired", return_value=True):
            with patch.object(client.auth.oidc, "valid", return_value=True):
                with patch("skaha.hooks.httpx.auth.oidc.refresh_token", return_value={"access_token": "new-token"}):
                    with patch.object(client, "_get_headers", return_value={"Authorization": "Bearer new-token"}):
                        with patch.object(client, "save") as mock_save:
                            # This should trigger the auth refresh
                            await async_hook(mock_request)

                            mock_save.assert_called_once()
                            assert mock_request.headers["Authorization"] == "Bearer new-token"

    def test_auth_hook_bypass_for_user_credentials(self):
        """Test that auth hooks bypass refresh for user-provided credentials."""
        from pydantic import SecretStr
        from skaha.client import SkahaClient

        # Test with user token
        client = SkahaClient(token=SecretStr("user-token"))
        sync_hook, _ = create_auth_hook(client)

        mock_request = Mock(spec=httpx.Request)

        with patch("skaha.hooks.httpx.auth._refresh_x509_sync") as mock_refresh:
            sync_hook(mock_request)
            mock_refresh.assert_not_called()

    def test_authentication_error_propagation(self):
        """Test that AuthenticationError is properly propagated."""
        from skaha.client import SkahaClient

        client = SkahaClient()
        client.auth.mode = "unknown"

        sync_hook, _ = create_auth_hook(client)
        mock_request = Mock(spec=httpx.Request)

        with patch.object(client.auth, "expired", return_value=True):
            with pytest.raises(AuthenticationError):
                sync_hook(mock_request)
