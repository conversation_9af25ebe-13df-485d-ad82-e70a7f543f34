"""HTTPx authentication hooks for automatic token refresh and certificate renewal.

This module provides httpx event hooks that automatically handle authentication
expiry and refresh for different authentication modes:

- **X509/Default Mode**: Automatically renews certificates when expired
- **OIDC Mode**: Automatically refreshes access tokens using refresh tokens
- **User-provided credentials**: Bypasses automatic refresh

The hooks are designed to be used with httpx clients to provide seamless
authentication management without requiring manual intervention.

Usage:
    ```python
    from skaha.client import SkahaClient
    from skaha.hooks.httpx.auth import create_auth_hook

    client = SkahaClient()
    auth_hook = create_auth_hook(client)

    # The hook is automatically applied to the client's httpx instances
    ```

Note:
    The hooks modify the request before it's sent, updating headers and
    authentication credentials as needed. They also save updated configuration
    to disk when credentials are refreshed.
"""

from __future__ import annotations

import asyncio
import time
from typing import TYPE_CHECKING, Callable

from skaha import get_logger
from skaha.auth import oidc, x509

if TYPE_CHECKING:
    from collections.abc import Awaitable

    import httpx

    from skaha.client import SkahaClient

log = get_logger(__name__)


class AuthenticationError(Exception):
    """Exception raised when authentication refresh fails."""


def create_auth_hook(
    client: SkahaClient,
) -> tuple[
    Callable[[httpx.Request], None],
    Callable[[httpx.Request], Awaitable[None]],
]:
    """Create authentication hooks for sync and async httpx clients.

    Args:
        client (SkahaClient): The SkahaClient instance containing auth configuration.

    Returns:
        tuple[Callable, Callable]: Tuple of (sync_hook, async_hook) functions.
    """

    def sync_auth_hook(request: httpx.Request) -> None:
        """Synchronous authentication hook for httpx requests.

        Args:
            request (httpx.Request): The outgoing HTTP request.
        """
        _handle_auth_sync(client, request)

    async def async_auth_hook(request: httpx.Request) -> None:
        """Asynchronous authentication hook for httpx requests.

        Args:
            request (httpx.Request): The outgoing HTTP request.
        """
        await _handle_auth_async(client, request)

    return sync_auth_hook, async_auth_hook


def _handle_auth_sync(client: SkahaClient, request: httpx.Request) -> None:
    """Handle authentication for synchronous requests.

    Args:
        client (SkahaClient): The SkahaClient instance.
        request (httpx.Request): The outgoing HTTP request.

    Raises:
        AuthenticationError: If authentication refresh fails.
    """
    # Skip refresh for user-provided credentials
    if client.token or client.certificate:
        log.debug("User-provided credentials detected, skipping auth refresh")
        return

    # Check if authentication is expired
    if not client.auth.expired():
        log.debug("Authentication not expired, continuing with request")
        return

    log.info(
        "Authentication expired, attempting refresh for mode: %s", client.auth.mode
    )

    try:
        if client.auth.mode in ("default", "x509"):
            _refresh_x509_sync(client)
        elif client.auth.mode == "oidc":
            _refresh_oidc_sync(client)
        else:
            msg = f"Unknown authentication mode: {client.auth.mode}"
            raise AuthenticationError(msg)

        # Update request headers with new authentication
        _update_request_headers(client, request)

        # Save updated configuration
        client.save()
        log.info("Authentication refreshed and configuration saved")

    except Exception as e:
        msg = f"Failed to refresh authentication: {e}"
        log.exception(msg)
        raise AuthenticationError(msg) from e


async def _handle_auth_async(client: SkahaClient, request: httpx.Request) -> None:
    """Handle authentication for asynchronous requests.

    Args:
        client (SkahaClient): The SkahaClient instance.
        request (httpx.Request): The outgoing HTTP request.

    Raises:
        AuthenticationError: If authentication refresh fails.
    """
    # Skip refresh for user-provided credentials
    if client.token or client.certificate:
        log.debug("User-provided credentials detected, skipping auth refresh")
        return

    # Check if authentication is expired
    if not client.auth.expired():
        log.debug("Authentication not expired, continuing with request")
        return

    log.info(
        "Authentication expired, attempting refresh for mode: %s", client.auth.mode
    )

    try:
        if client.auth.mode in ("default", "x509"):
            await _refresh_x509_async(client)
        elif client.auth.mode == "oidc":
            await _refresh_oidc_async(client)
        else:
            msg = f"Unknown authentication mode: {client.auth.mode}"
            raise AuthenticationError(msg)

        # Update request headers with new authentication
        _update_request_headers(client, request)

        # Save updated configuration
        client.save()
        log.info("Authentication refreshed and configuration saved")

    except Exception as e:
        msg = f"Failed to refresh authentication: {e}"
        log.exception(msg)
        raise AuthenticationError(msg) from e


def _refresh_x509_sync(client: SkahaClient) -> None:
    """Refresh X509 certificate synchronously.

    Args:
        client (SkahaClient): The SkahaClient instance.
    """
    log.debug("Refreshing X509 certificate")

    if client.auth.mode == "default":
        client.auth.default = x509.authenticate(client.auth.default)
    else:  # x509 mode
        client.auth.x509 = x509.authenticate(client.auth.x509)


async def _refresh_x509_async(client: SkahaClient) -> None:
    """Refresh X509 certificate asynchronously.

    Args:
        client (SkahaClient): The SkahaClient instance.
    """
    log.debug("Refreshing X509 certificate (async)")

    # Run the sync x509 authentication in a thread pool
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, _refresh_x509_sync, client)


def _refresh_oidc_sync(client: SkahaClient) -> None:
    """Refresh OIDC tokens synchronously.

    Args:
        client (SkahaClient): The SkahaClient instance.

    Raises:
        AuthenticationError: If refresh token is invalid or expired.
    """
    log.debug("Refreshing OIDC tokens")

    # Check if we have valid refresh token configuration
    if not client.auth.oidc.valid():
        msg = "OIDC configuration is invalid for token refresh"
        raise AuthenticationError(msg)

    # Run async refresh in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(_refresh_oidc_async(client))
    finally:
        loop.close()


async def _refresh_oidc_async(client: SkahaClient) -> None:
    """Refresh OIDC tokens asynchronously.

    Args:
        client (SkahaClient): The SkahaClient instance.

    Raises:
        AuthenticationError: If refresh token is invalid or expired.
    """
    log.debug("Refreshing OIDC tokens (async)")

    # Check if we have valid refresh token configuration
    if not client.auth.oidc.valid():
        msg = "OIDC configuration is invalid for token refresh"
        raise AuthenticationError(msg)

    try:
        # Use the refresh token to get new access token
        tokens = await oidc.refresh(
            url=str(client.auth.oidc.endpoints.token),
            identity=str(client.auth.oidc.client.identity),
            secret=str(client.auth.oidc.client.secret),
            token=str(client.auth.oidc.token.refresh),
        )

        # Update tokens and expiry
        client.auth.oidc.token.access = tokens["access_token"]

        # Update access token expiry if provided
        if "expires_in" in tokens:
            client.auth.oidc.expiry.access = time.time() + tokens["expires_in"]

        # Update refresh token if a new one was provided
        if "refresh_token" in tokens:
            client.auth.oidc.token.refresh = tokens["refresh_token"]

        log.debug("OIDC tokens refreshed successfully")

    except ValueError as e:
        # Refresh token is invalid/expired, need full re-authentication
        msg = f"Refresh token invalid, full re-authentication required: {e}"
        log.exception(msg)
        raise AuthenticationError(msg) from e


def _update_request_headers(client: SkahaClient, request: httpx.Request) -> None:
    """Update request headers with refreshed authentication.

    Args:
        client (SkahaClient): The SkahaClient instance.
        request (httpx.Request): The HTTP request to update.
    """
    log.debug("Updating request headers with refreshed authentication")

    # Get fresh headers from the client
    fresh_headers = client._get_headers()

    # Update the request headers
    for key, value in fresh_headers.items():
        request.headers[key] = value

    log.debug("Request headers updated successfully")
