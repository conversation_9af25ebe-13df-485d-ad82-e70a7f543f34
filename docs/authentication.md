# Authentication System

The Skaha authentication system provides comprehensive support for multiple authentication methods with automatic credential management, expiry tracking, and seamless refresh capabilities.

## Overview

!!! tip "Automatic Authentication Management"
    Starting with v1.7, Skaha features an enhanced authentication system that automatically handles credential expiry and refresh, ensuring uninterrupted access to CANFAR Science Platform services.

### Key Features

- **Multiple Authentication Methods**: X.509 certificates, OIDC tokens, and bearer tokens
- **Automatic Refresh**: Seamless credential renewal without user intervention
- **Expiry Tracking**: Real-time monitoring of authentication status
- **Priority-based Selection**: Intelligent authentication method selection
- **Secure Storage**: Safe credential management and configuration persistence

## Authentication Modes

### X.509 Certificate Authentication

X.509 certificates provide secure, long-term authentication for CANFAR services.

#### Default Mode

The default authentication mode uses the standard CADC certificate location:

```python
from skaha.client import SkahaClient

# Uses default certificate at ~/.ssl/cadcproxy.pem
client = SkahaClient()
```

!!! info "Default Certificate Location"
    The default certificate is automatically looked up at `$HOME/.ssl/cadcproxy.pem`.

#### Custom X.509 Mode

For custom certificate locations, configure the X.509 authentication mode:

```python
from pathlib import Path
from skaha.client import SkahaClient

# Configure custom certificate path
client = SkahaClient()
client.auth.mode = "x509"
client.auth.x509.path = Path("/custom/path/to/cert.pem")
```

#### Certificate Generation

Generate a new certificate using the CLI:

```bash
# Interactive certificate generation
skaha auth login

# Or use the CADC certificate tool directly
cadc-get-cert --days-valid=10
```

### OIDC Token Authentication

OpenID Connect (OIDC) provides modern, token-based authentication with automatic refresh capabilities.

#### Configuration

```python
from skaha.client import SkahaClient

# Configure OIDC authentication
client = SkahaClient()
client.auth.mode = "oidc"

# OIDC configuration is typically set via CLI
# skaha auth login
```

#### Token Refresh

OIDC tokens are automatically refreshed when they expire:

!!! success "Automatic Token Refresh"
    The authentication system automatically detects expired access tokens and uses refresh tokens to obtain new credentials without user intervention.

### Bearer Token Authentication

For direct token usage, bypass the configuration system:

```python
from pydantic import SecretStr
from skaha.client import SkahaClient

# Use bearer token directly
token = SecretStr("your-bearer-token-here")
client = SkahaClient(token=token)
```

!!! warning "Token Security"
    Always use `SecretStr` for tokens to prevent accidental logging of sensitive credentials.

## Authentication Priority

The authentication system follows a strict priority order:

1. **User-provided token** (highest priority)
2. **User-provided certificate**
3. **Configured authentication mode** (oidc, x509, default)
4. **Default certificate** (fallback)

```python
from pathlib import Path
from pydantic import SecretStr
from skaha.client import SkahaClient

# Priority 1: User-provided token (overrides everything)
client = SkahaClient(token=SecretStr("token"))

# Priority 2: User-provided certificate
client = SkahaClient(certificate=Path("/path/to/cert.pem"))

# Priority 3: Configured authentication mode
client = SkahaClient()  # Uses client.auth.mode setting

# Priority 4: Default certificate (automatic fallback)
```

## Automatic Authentication Refresh

The authentication system includes sophisticated automatic refresh capabilities that work transparently in the background.

### How It Works

1. **Expiry Detection**: Before each HTTP request, the system checks if credentials are expired
2. **Automatic Refresh**: If expired, credentials are automatically refreshed based on the authentication mode
3. **Request Continuation**: The original request proceeds with fresh credentials
4. **Configuration Persistence**: Updated credentials are automatically saved to disk

### Refresh Behavior by Mode

#### X.509 Certificate Refresh

```python
# When X.509 certificates expire, the system automatically:
# 1. Detects certificate expiry
# 2. Generates a new certificate using stored credentials
# 3. Updates the configuration
# 4. Continues with the request
```

#### OIDC Token Refresh

```python
# When OIDC access tokens expire, the system automatically:
# 1. Detects access token expiry
# 2. Uses the refresh token to obtain a new access token
# 3. Updates token expiry information
# 4. Saves the new configuration
# 5. Continues with the request
```

### Refresh Token Expiry

If the OIDC refresh token itself is expired, the system will raise an `AuthenticationError`:

```python
from skaha.hooks.httpx.auth import AuthenticationError

try:
    response = client.client.get("/session")
except AuthenticationError as e:
    print(f"Authentication failed: {e}")
    # User needs to re-authenticate via CLI
    # skaha auth login
```

## Checking Authentication Status

### Current Authentication Mode

```python
from skaha.client import SkahaClient

client = SkahaClient()
print(f"Authentication mode: {client.auth.mode}")
```

### Expiry Information

```python
import time
from skaha.client import SkahaClient

client = SkahaClient()

if client.expiry:
    time_left = client.expiry - time.time()
    if time_left > 0:
        print(f"Authentication expires in {time_left:.0f} seconds")
    else:
        print("Authentication is expired")
else:
    print("No expiry tracking (user-provided credentials)")
```

### Validation

```python
from skaha.client import SkahaClient

client = SkahaClient()

# Check if current authentication is valid
if client.auth.valid():
    print("Authentication configuration is valid")
else:
    print("Authentication configuration is invalid")

# Check if authentication is expired
if client.auth.expired():
    print("Authentication is expired")
else:
    print("Authentication is active")
```

## Configuration Management

### Viewing Configuration

```bash
# View current authentication configuration
skaha config show
```

### Clearing Authentication

```bash
# Clear all authentication credentials
skaha auth logout
```

### Manual Configuration

Authentication can be configured programmatically:

```python
from skaha.models.config import Configuration

# Load and modify configuration
config = Configuration()
config.auth.mode = "oidc"
config.save()
```

## Troubleshooting

### Common Issues

#### Certificate Not Found

```
FileNotFoundError: cert file /path/to/cert.pem does not exist
```

**Solution**: Generate a new certificate or update the path:

```bash
skaha auth login
```

#### OIDC Refresh Failed

```
AuthenticationError: Refresh token invalid, full re-authentication required
```

**Solution**: Re-authenticate via CLI:

```bash
skaha auth login
```

#### Permission Denied

```
PermissionError: cert file /path/to/cert.pem is not readable
```

**Solution**: Fix file permissions:

```bash
chmod 600 /path/to/cert.pem
```

### Debug Logging

Enable debug logging to troubleshoot authentication issues:

```python
import logging
from skaha.client import SkahaClient

# Enable debug logging
client = SkahaClient(loglevel=logging.DEBUG)

# This will log:
# - Authentication mode selection
# - Expiry checking
# - Refresh attempts
# - Configuration updates
```

## Security Considerations

!!! danger "Security Best Practices"
    - Never commit certificates or tokens to version control
    - Use `SecretStr` for sensitive credentials in code
    - Regularly rotate certificates and tokens
    - Monitor authentication logs for suspicious activity
    - Use the most restrictive authentication method for your use case

### Credential Storage

- **Certificates**: Stored as PEM files with 600 permissions
- **Configuration**: Stored in YAML format at `~/.config/skaha/config.yaml`
- **Tokens**: Stored encrypted in the configuration file

### Network Security

- All communications use HTTPS/TLS encryption
- Certificate-based authentication provides mutual TLS
- OIDC tokens are transmitted securely via Authorization headers

## Technical Implementation

### HTTPx Authentication Hooks

The automatic authentication refresh is implemented using HTTPx event hooks that intercept requests before they are sent:

```python
# The authentication system uses HTTPx request hooks
# These hooks are automatically configured when creating SkahaClient instances

from skaha.client import SkahaClient

client = SkahaClient()
# Authentication hooks are automatically attached to both
# client.client (sync) and client.asynclient (async)
```

#### Hook Behavior

1. **Request Interception**: Every outgoing HTTP request is intercepted
2. **Expiry Check**: The hook checks if current credentials are expired
3. **Conditional Refresh**: If expired, appropriate refresh logic is executed
4. **Header Update**: Request headers are updated with fresh credentials
5. **Request Continuation**: The request proceeds with valid authentication

#### Bypass Conditions

The authentication hooks automatically bypass refresh in these scenarios:

- **User-provided tokens**: When `SkahaClient(token=...)` is used
- **User-provided certificates**: When `SkahaClient(certificate=...)` is used
- **Valid credentials**: When current credentials are not expired

### Error Handling

The authentication system provides comprehensive error handling:

#### AuthenticationError

Raised when authentication refresh fails:

```python
from skaha.hooks.httpx.auth import AuthenticationError

try:
    response = client.client.get("/session")
except AuthenticationError as e:
    # Handle authentication failure
    print(f"Authentication failed: {e}")
    # Typically requires user re-authentication
```

#### Common Error Scenarios

1. **Expired Refresh Token**: OIDC refresh token is no longer valid
2. **Invalid Configuration**: Missing required authentication parameters
3. **Network Errors**: Unable to reach authentication endpoints
4. **Certificate Issues**: Certificate file not found or corrupted

### Performance Considerations

#### Caching

- **Expiry Checks**: Minimal overhead, uses cached expiry timestamps
- **Hook Execution**: Only executes refresh logic when credentials are expired
- **Configuration Updates**: Batched writes to minimize I/O operations

#### Concurrency

- **Thread Safety**: Authentication hooks are thread-safe for concurrent requests
- **Async Support**: Full support for asyncio-based applications
- **Connection Pooling**: Maintains separate connection pools for different auth modes

## Advanced Usage

### Custom Authentication Flows

For advanced use cases, you can implement custom authentication:

```python
from skaha.auth import oidc, x509
from skaha.models.auth import OIDC, X509

# Custom OIDC authentication
oidc_config = OIDC()
oidc_config.endpoints.token = "https://custom-oidc.example.com/token"
oidc_config.client.identity = "custom-client-id"
# ... configure other parameters

# Custom X.509 authentication
x509_config = X509()
x509_config.path = Path("/custom/cert.pem")
# ... configure other parameters
```

### Programmatic Token Refresh

Manually refresh tokens when needed:

```python
import asyncio
from skaha.auth.oidc import refresh_token

async def manual_refresh():
    tokens = await refresh_token(
        token_url="https://oidc.example.com/token",
        client_id="your-client-id",
        client_secret="your-client-secret",
        refresh_token="your-refresh-token"
    )
    return tokens["access_token"]

# Use in async context
new_token = asyncio.run(manual_refresh())
```

### Configuration Validation

Validate authentication configuration before use:

```python
from skaha.models.config import Configuration

config = Configuration.assemble()

# Validate specific authentication modes
if config.auth.mode == "oidc":
    if config.auth.oidc.valid():
        print("OIDC configuration is valid")
    else:
        print("OIDC configuration is missing required fields")

if config.auth.mode == "x509":
    if config.auth.x509.valid():
        print("X.509 configuration is valid")
    else:
        print("X.509 configuration is invalid")
```

## Migration Guide

### From Manual Token Management

If you were previously managing tokens manually:

```python
# Old approach (manual token management)
import httpx
token = "manually-managed-token"
headers = {"Authorization": f"Bearer {token}"}
response = httpx.get("https://api.example.com", headers=headers)

# New approach (automatic management)
from skaha.client import SkahaClient
client = SkahaClient()  # Automatically handles authentication
response = client.client.get("/endpoint")  # No manual token management needed
```

### From Certificate-only Authentication

Migrating from certificate-only setups:

```python
# Old approach (certificate only)
import ssl
import httpx
ssl_context = ssl.create_default_context()
ssl_context.load_cert_chain("/path/to/cert.pem")
client = httpx.Client(verify=ssl_context)

# New approach (automatic certificate management)
from skaha.client import SkahaClient
client = SkahaClient()  # Automatically handles certificates and refresh
```

## API Reference

### Authentication Models

- [`Authentication`](../api/models/auth/#authentication): Main authentication configuration
- [`OIDC`](../api/models/auth/#oidc): OIDC-specific configuration
- [`X509`](../api/models/auth/#x509): X.509 certificate configuration

### Authentication Functions

- [`oidc.refresh_token()`](../api/auth/oidc/#refresh_token): Manual OIDC token refresh
- [`x509.authenticate()`](../api/auth/x509/#authenticate): X.509 certificate authentication
- [`create_auth_hook()`](../api/hooks/httpx/auth/#create_auth_hook): Create HTTPx authentication hooks

### Configuration Management

- [`Configuration.assemble()`](../api/models/config/#assemble): Load configuration from file
- [`Configuration.save()`](../api/models/config/#save): Save configuration to file
